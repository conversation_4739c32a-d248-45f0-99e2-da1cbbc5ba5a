# Makefile dla Klimatyzacja SMS Manager
# Autor: Augment Code Assistant

.PHONY: help install deploy status logs restart backup monitor clean

# Zmienne
APP_NAME = klimatyzacja-sms-manager
SCRIPTS_DIR = scripts
DOCS_DIR = docs

# Domyślny target
help: ## <PERSON><PERSON><PERSON> dostępne komendy
	@echo "🌡️  Klimatyzacja SMS Manager - Dostępne komendy:"
	@echo ""
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "  \033[36m%-15s\033[0m %s\n", $$1, $$2}'
	@echo ""

install: ## Instalacja aplikacji (one-click)
	@echo "🚀 Rozpoczynam instalację..."
	chmod +x install.sh
	./install.sh

setup-vps: ## Konfiguracja VPS
	@echo "🖥️  Konfiguracja VPS..."
	chmod +x $(SCRIPTS_DIR)/setup-vps.sh
	@read -p "Podaj domenę (lub Enter dla localhost): " domain; \
	./$(SCRIPTS_DIR)/setup-vps.sh $$domain

build: ## Budowanie aplikacji
	@echo "🔨 Budowanie aplikacji..."
	npm install
	npm run build

deploy: ## Deployment aplikacji
	@echo "🚀 Deployment aplikacji..."
	chmod +x $(SCRIPTS_DIR)/deploy.sh
	./$(SCRIPTS_DIR)/deploy.sh

pm2-setup: ## Konfiguracja PM2
	@echo "⚙️  Konfiguracja PM2..."
	chmod +x $(SCRIPTS_DIR)/pm2-setup.sh
	./$(SCRIPTS_DIR)/pm2-setup.sh

nginx-setup: ## Konfiguracja Nginx
	@echo "🌐 Konfiguracja Nginx..."
	sudo cp config/nginx.conf /etc/nginx/sites-available/$(APP_NAME)
	@read -p "Podaj domenę: " domain; \
	sudo sed -i "s/your-domain.com/$$domain/g" /etc/nginx/sites-available/$(APP_NAME)
	sudo ln -sf /etc/nginx/sites-available/$(APP_NAME) /etc/nginx/sites-enabled/
	sudo rm -f /etc/nginx/sites-enabled/default
	sudo nginx -t && sudo systemctl restart nginx

ssl-setup: ## Konfiguracja SSL
	@echo "🔒 Konfiguracja SSL..."
	chmod +x $(SCRIPTS_DIR)/setup-ssl.sh
	@read -p "Podaj domenę: " domain; \
	read -p "Podaj email: " email; \
	./$(SCRIPTS_DIR)/setup-ssl.sh $$domain $$email

security-setup: ## Konfiguracja bezpieczeństwa
	@echo "🛡️  Konfiguracja bezpieczeństwa..."
	chmod +x $(SCRIPTS_DIR)/security-setup.sh
	./$(SCRIPTS_DIR)/security-setup.sh

status: ## Status aplikacji i systemu
	@echo "📊 Status aplikacji i systemu:"
	@echo ""
	@echo "=== PM2 Status ==="
	pm2 status
	@echo ""
	@echo "=== Nginx Status ==="
	systemctl status nginx --no-pager -l
	@echo ""
	@echo "=== Zasoby Systemowe ==="
	@echo "Dysk: $$(df -h / | awk 'NR==2 {print $$5}')"
	@echo "RAM: $$(free -h | grep Mem | awk '{print $$3"/"$$2}')"
	@echo "Load: $$(uptime | awk -F'load average:' '{print $$2}')"

logs: ## Pokaż logi aplikacji
	@echo "📋 Logi aplikacji:"
	pm2 logs $(APP_NAME) --lines 50

logs-nginx: ## Pokaż logi Nginx
	@echo "📋 Logi Nginx:"
	sudo tail -50 /var/log/nginx/access.log
	@echo ""
	@echo "=== Błędy Nginx ==="
	sudo tail -20 /var/log/nginx/error.log

restart: ## Restart aplikacji
	@echo "🔄 Restart aplikacji..."
	pm2 restart $(APP_NAME)
	sudo systemctl restart nginx

stop: ## Zatrzymaj aplikację
	@echo "⏹️  Zatrzymywanie aplikacji..."
	pm2 stop $(APP_NAME)

start: ## Uruchom aplikację
	@echo "▶️  Uruchamianie aplikacji..."
	pm2 start ecosystem.config.js

backup: ## Utwórz backup
	@echo "💾 Tworzenie backup..."
	chmod +x $(SCRIPTS_DIR)/backup.sh
	./$(SCRIPTS_DIR)/backup.sh

monitor: ## Uruchom monitoring
	@echo "📊 Monitoring systemu..."
	chmod +x $(SCRIPTS_DIR)/monitoring.sh
	./$(SCRIPTS_DIR)/monitoring.sh

monitor-setup: ## Konfiguracja automatycznego monitoringu
	@echo "⚙️  Konfiguracja automatycznego monitoringu..."
	chmod +x $(SCRIPTS_DIR)/monitoring.sh
	@echo "Dodawanie do crontab..."
	(crontab -l 2>/dev/null; echo "*/5 * * * * $$(pwd)/$(SCRIPTS_DIR)/monitoring.sh") | crontab -
	@echo "✅ Monitoring skonfigurowany (co 5 minut)"

health-check: ## Sprawdź czy aplikacja działa
	@echo "🏥 Health check..."
	@if curl -f -s http://localhost:3000 > /dev/null; then \
		echo "✅ Aplikacja działa poprawnie!"; \
	else \
		echo "❌ Aplikacja nie odpowiada!"; \
		echo "Sprawdź status: make status"; \
		echo "Sprawdź logi: make logs"; \
	fi

update: ## Aktualizuj aplikację z repozytorium
	@echo "🔄 Aktualizacja aplikacji..."
	git pull origin main
	npm install
	npm run build
	pm2 restart $(APP_NAME)

clean: ## Wyczyść pliki tymczasowe
	@echo "🧹 Czyszczenie..."
	rm -rf node_modules/.cache
	rm -rf dist
	npm cache clean --force

clean-logs: ## Wyczyść stare logi
	@echo "🧹 Czyszczenie logów..."
	pm2 flush
	sudo truncate -s 0 /var/log/nginx/access.log
	sudo truncate -s 0 /var/log/nginx/error.log

permissions: ## Napraw uprawnienia skryptów
	@echo "🔧 Naprawa uprawnień..."
	chmod +x install.sh
	chmod +x $(SCRIPTS_DIR)/*.sh
	@echo "✅ Uprawnienia naprawione"

docs: ## Otwórz dokumentację
	@echo "📚 Dokumentacja:"
	@echo "  Przewodnik wdrożenia: $(DOCS_DIR)/deployment-guide.md"
	@echo "  Rozwiązywanie problemów: $(DOCS_DIR)/troubleshooting.md"
	@echo "  README: README-DEPLOYMENT.md"

test-nginx: ## Test konfiguracji Nginx
	@echo "🧪 Test konfiguracji Nginx..."
	sudo nginx -t

reload-nginx: ## Przeładuj Nginx
	@echo "🔄 Przeładowanie Nginx..."
	sudo systemctl reload nginx

firewall-status: ## Status firewall
	@echo "🔥 Status firewall:"
	sudo ufw status verbose

fail2ban-status: ## Status Fail2Ban
	@echo "🛡️  Status Fail2Ban:"
	sudo fail2ban-client status

ssl-status: ## Status certyfikatu SSL
	@echo "🔒 Status certyfikatu SSL:"
	@if [ -f "/etc/letsencrypt/live/*/cert.pem" ]; then \
		CERT_FILE=$$(find /etc/letsencrypt/live -name "cert.pem" | head -1); \
		openssl x509 -enddate -noout -in "$$CERT_FILE"; \
	else \
		echo "Brak certyfikatu SSL"; \
	fi

# Aliasy dla wygody
s: status ## Alias dla status
l: logs ## Alias dla logs
r: restart ## Alias dla restart
m: monitor ## Alias dla monitor
b: backup ## Alias dla backup
h: health-check ## Alias dla health-check
