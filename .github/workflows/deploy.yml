name: Deploy to VPS

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

env:
  NODE_VERSION: '18'
  APP_NAME: 'klimatyzacja-sms-manager'

jobs:
  # Job 1: Build and Test
  build-and-test:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run linter
      run: npm run lint

    - name: Build application
      run: npm run build
      env:
        VITE_SUPABASE_URL: ${{ secrets.VITE_SUPABASE_URL }}
        VITE_SUPABASE_ANON_KEY: ${{ secrets.VITE_SUPABASE_ANON_KEY }}

    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: dist
        path: dist/
        retention-days: 1

  # Job 2: Deploy to VPS (tylko dla main branch)
  deploy:
    needs: build-and-test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Download build artifacts
      uses: actions/download-artifact@v4
      with:
        name: dist
        path: dist/

    - name: Setup SSH
      uses: webfactory/ssh-agent@v0.7.0
      with:
        ssh-private-key: ${{ secrets.VPS_SSH_KEY }}

    - name: Add VPS to known hosts
      run: |
        ssh-keyscan -H ${{ secrets.VPS_HOST }} >> ~/.ssh/known_hosts

    - name: Deploy to VPS
      run: |
        # Utworzenie skryptu deploymentu
        cat > deploy_script.sh << 'EOF'
        #!/bin/bash
        set -e
        
        APP_DIR="/home/<USER>/${{ env.APP_NAME }}"
        BACKUP_DIR="/home/<USER>/backups"
        TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
        
        echo "🚀 Rozpoczynam deployment..."
        
        # Przejdź do katalogu aplikacji
        cd "$APP_DIR"
        
        # Backup obecnej wersji
        echo "📦 Tworzenie backupu..."
        mkdir -p "$BACKUP_DIR"
        if [ -d "dist" ]; then
          tar -czf "$BACKUP_DIR/dist_backup_$TIMESTAMP.tar.gz" dist/
        fi
        
        # Zatrzymaj aplikację
        echo "⏹️ Zatrzymywanie aplikacji..."
        pm2 stop ${{ env.APP_NAME }} || true
        
        # Aktualizuj kod
        echo "📥 Aktualizacja kodu..."
        git fetch origin
        git reset --hard origin/main
        
        # Zainstaluj zależności
        echo "📦 Instalacja zależności..."
        npm ci --production=false
        
        # Buduj aplikację
        echo "🔨 Budowanie aplikacji..."
        npm run build
        
        # Uruchom aplikację
        echo "▶️ Uruchamianie aplikacji..."
        pm2 start ecosystem.config.js --env production || pm2 restart ${{ env.APP_NAME }}
        
        # Sprawdź status
        echo "✅ Sprawdzanie statusu..."
        sleep 5
        pm2 status
        
        # Health check
        if curl -f -s http://localhost:3000 > /dev/null; then
          echo "✅ Deployment zakończony pomyślnie!"
        else
          echo "❌ Aplikacja nie odpowiada!"
          exit 1
        fi
        EOF
        
        # Wykonaj deployment na VPS
        ssh ${{ secrets.VPS_USER }}@${{ secrets.VPS_HOST }} 'bash -s' < deploy_script.sh

    - name: Health Check
      run: |
        # Sprawdź czy aplikacja działa
        sleep 10
        if curl -f -s http://${{ secrets.VPS_HOST }} > /dev/null; then
          echo "✅ Aplikacja działa poprawnie!"
        else
          echo "❌ Health check nie powiódł się!"
          exit 1
        fi

    - name: Notify on success
      if: success()
      run: |
        echo "🎉 Deployment zakończony pomyślnie!"
        echo "🌐 Aplikacja dostępna pod: http://${{ secrets.VPS_HOST }}"

    - name: Notify on failure
      if: failure()
      run: |
        echo "❌ Deployment nie powiódł się!"
        echo "🔍 Sprawdź logi powyżej"

  # Job 3: Deploy to staging (dla PR)
  deploy-staging:
    needs: build-and-test
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Download build artifacts
      uses: actions/download-artifact@v4
      with:
        name: dist
        path: dist/

    - name: Deploy to staging
      run: |
        echo "🚧 Deployment do środowiska staging..."
        echo "📝 PR #${{ github.event.number }}: ${{ github.event.pull_request.title }}"
        echo "🔗 Preview będzie dostępny pod: http://staging.${{ secrets.VPS_HOST }}"
        # Tutaj możesz dodać logikę deploymentu do staging
