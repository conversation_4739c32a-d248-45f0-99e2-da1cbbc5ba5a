name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  NODE_VERSION: '18'

jobs:
  # Job 1: Code Quality Checks
  quality-checks:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run ESLint
      run: npm run lint

    - name: Check TypeScript
      run: npx tsc --noEmit

    - name: Check formatting (if Prettier is configured)
      run: |
        if [ -f ".prettierrc" ] || [ -f "prettier.config.js" ]; then
          npx prettier --check .
        else
          echo "Prettier not configured, skipping format check"
        fi

  # Job 2: Security Audit
  security-audit:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run security audit
      run: npm audit --audit-level=moderate

    - name: Check for known vulnerabilities
      run: |
        if command -v snyk &> /dev/null; then
          snyk test
        else
          echo "Snyk not available, skipping vulnerability check"
        fi

  # Job 3: Build Test
  build-test:
    runs-on: ubuntu-latest
    needs: [quality-checks]
    
    strategy:
      matrix:
        node-version: [16, 18, 20]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Build application
      run: npm run build
      env:
        VITE_SUPABASE_URL: ${{ secrets.VITE_SUPABASE_URL || 'https://dummy.supabase.co' }}
        VITE_SUPABASE_ANON_KEY: ${{ secrets.VITE_SUPABASE_ANON_KEY || 'dummy-key' }}

    - name: Check build output
      run: |
        if [ ! -d "dist" ]; then
          echo "❌ Build failed - no dist directory"
          exit 1
        fi
        
        if [ ! -f "dist/index.html" ]; then
          echo "❌ Build failed - no index.html"
          exit 1
        fi
        
        echo "✅ Build successful"
        ls -la dist/

  # Job 4: Dependency Check
  dependency-check:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Check for outdated dependencies
      run: |
        npm outdated || true
        echo "ℹ️ Sprawdź powyższe zależności do aktualizacji"

    - name: Check package-lock.json
      run: |
        if [ -f "package-lock.json" ]; then
          npm ci --dry-run
          echo "✅ package-lock.json jest aktualny"
        else
          echo "⚠️ Brak package-lock.json"
        fi

  # Job 5: Performance Check
  performance-check:
    runs-on: ubuntu-latest
    needs: [build-test]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Build application
      run: npm run build
      env:
        VITE_SUPABASE_URL: ${{ secrets.VITE_SUPABASE_URL || 'https://dummy.supabase.co' }}
        VITE_SUPABASE_ANON_KEY: ${{ secrets.VITE_SUPABASE_ANON_KEY || 'dummy-key' }}

    - name: Analyze bundle size
      run: |
        echo "📊 Analiza rozmiaru bundle:"
        du -sh dist/
        find dist/ -name "*.js" -exec du -h {} \; | sort -hr
        find dist/ -name "*.css" -exec du -h {} \; | sort -hr

    - name: Check for large files
      run: |
        echo "🔍 Sprawdzanie dużych plików (>1MB):"
        find dist/ -size +1M -type f -exec ls -lh {} \; || echo "Brak dużych plików"

  # Job 6: Notification
  notify:
    runs-on: ubuntu-latest
    needs: [quality-checks, security-audit, build-test, dependency-check, performance-check]
    if: always()
    
    steps:
    - name: Notify success
      if: ${{ needs.quality-checks.result == 'success' && needs.security-audit.result == 'success' && needs.build-test.result == 'success' }}
      run: |
        echo "✅ Wszystkie testy przeszły pomyślnie!"
        echo "🚀 Gotowe do deploymentu"

    - name: Notify failure
      if: ${{ needs.quality-checks.result == 'failure' || needs.security-audit.result == 'failure' || needs.build-test.result == 'failure' }}
      run: |
        echo "❌ Niektóre testy nie powiodły się!"
        echo "🔍 Sprawdź logi powyżej"
