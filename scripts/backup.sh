#!/bin/bash

# Skrypt backup dla aplikacji klimatyzacja-sms-manager
# Autor: Augment Code Assistant

set -e

# Kolory dla outputu
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Zmienne konfiguracyjne
APP_NAME="klimatyzacja-sms-manager"
APP_DIR="/home/<USER>/$APP_NAME"
BACKUP_DIR="/home/<USER>/backups"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
RETENTION_DAYS=30

# Utworzenie katalogu backupów
mkdir -p "$BACKUP_DIR"

log_info "Rozpoczynam backup aplikacji $APP_NAME"

# Backup kodu aplikacji
log_info "Backup kodu aplikacji..."
cd "$APP_DIR"
tar -czf "$BACKUP_DIR/app_backup_$TIMESTAMP.tar.gz" \
    --exclude=node_modules \
    --exclude=dist \
    --exclude=.git \
    .

# Backup plików konfiguracyjnych
log_info "Backup plików konfiguracyjnych..."
tar -czf "$BACKUP_DIR/config_backup_$TIMESTAMP.tar.gz" \
    .env \
    ecosystem.config.js \
    /etc/nginx/sites-available/$APP_NAME 2>/dev/null || true

# Backup bazy danych Supabase (jeśli masz dostęp do dump)
# Uwaga: To wymaga konfiguracji dostępu do Supabase CLI
if command -v supabase &> /dev/null; then
    log_info "Backup bazy danych Supabase..."
    supabase db dump --file "$BACKUP_DIR/supabase_backup_$TIMESTAMP.sql" 2>/dev/null || log_warn "Nie udało się wykonać backup bazy danych"
fi

# Backup zmiennych środowiskowych (bez wrażliwych danych)
log_info "Backup konfiguracji środowiska..."
if [ -f "$APP_DIR/.env" ]; then
    # Utwórz kopię .env bez wrażliwych kluczy
    grep -v -E "(KEY|SECRET|PASSWORD|TOKEN)" "$APP_DIR/.env" > "$BACKUP_DIR/env_template_$TIMESTAMP.txt" || true
fi

# Backup logów PM2
log_info "Backup logów PM2..."
if [ -d "/home/<USER>/.pm2/logs" ]; then
    tar -czf "$BACKUP_DIR/logs_backup_$TIMESTAMP.tar.gz" /home/<USER>/.pm2/logs/
fi

# Czyszczenie starych backupów
log_info "Czyszczenie backupów starszych niż $RETENTION_DAYS dni..."
find "$BACKUP_DIR" -name "*_backup_*.tar.gz" -mtime +$RETENTION_DAYS -delete
find "$BACKUP_DIR" -name "*_backup_*.sql" -mtime +$RETENTION_DAYS -delete

# Podsumowanie
log_info "Backup zakończony pomyślnie!"
log_info "Lokalizacja backupów: $BACKUP_DIR"
log_info "Utworzone pliki:"
ls -la "$BACKUP_DIR"/*_$TIMESTAMP.*

# Opcjonalnie: Upload do cloud storage
# Odkomentuj i skonfiguruj jeśli chcesz automatyczny upload do AWS S3, Google Cloud, itp.
# aws s3 cp "$BACKUP_DIR/" s3://your-backup-bucket/ --recursive --exclude "*" --include "*_$TIMESTAMP.*"
