#!/bin/bash

# Skrypt konfiguracji SSL z Let's Encrypt dla aplikacji klimatyzacja-sms-manager
# Autor: Augment Code Assistant

set -e

# Kolory dla outputu
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Sprawdź argumenty
if [ $# -eq 0 ]; then
    log_error "Użycie: $0 <domena>"
    log_error "Przykład: $0 example.com"
    exit 1
fi

DOMAIN=$1
EMAIL=${2:-admin@$DOMAIN}

log_info "Konfiguracja SSL dla domeny: $DOMAIN"
log_info "Email: $EMAIL"

# Sprawdź czy domena wskazuje na serwer
log_info "Sprawdzanie DNS dla domeny $DOMAIN..."
SERVER_IP=$(curl -s ifconfig.me)
DOMAIN_IP=$(dig +short $DOMAIN)

if [ "$DOMAIN_IP" != "$SERVER_IP" ]; then
    log_warn "Domena $DOMAIN nie wskazuje na ten serwer ($SERVER_IP)"
    log_warn "Aktualne IP domeny: $DOMAIN_IP"
    read -p "Czy chcesz kontynuować? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Instalacja Certbot
log_info "Instalacja Certbot..."
sudo apt update
sudo apt install -y snapd
sudo snap install core; sudo snap refresh core
sudo snap install --classic certbot

# Utworzenie linku symbolicznego
sudo ln -sf /snap/bin/certbot /usr/bin/certbot

# Zatrzymanie Nginx tymczasowo
log_info "Zatrzymywanie Nginx..."
sudo systemctl stop nginx

# Uzyskanie certyfikatu SSL
log_info "Uzyskiwanie certyfikatu SSL..."
sudo certbot certonly --standalone \
    --non-interactive \
    --agree-tos \
    --email "$EMAIL" \
    -d "$DOMAIN" \
    -d "www.$DOMAIN"

# Aktualizacja konfiguracji Nginx
log_info "Aktualizacja konfiguracji Nginx..."
sudo sed -i "s/your-domain.com/$DOMAIN/g" /etc/nginx/sites-available/klimatyzacja-sms-manager

# Test konfiguracji Nginx
log_info "Test konfiguracji Nginx..."
sudo nginx -t

# Uruchomienie Nginx
log_info "Uruchamianie Nginx..."
sudo systemctl start nginx

# Konfiguracja automatycznego odnawiania
log_info "Konfiguracja automatycznego odnawiania..."
sudo crontab -l 2>/dev/null | { cat; echo "0 12 * * * /usr/bin/certbot renew --quiet --post-hook 'systemctl reload nginx'"; } | sudo crontab -

# Test SSL
log_info "Test SSL..."
sleep 5
if curl -f -s https://$DOMAIN > /dev/null; then
    log_info "✅ SSL skonfigurowane pomyślnie!"
    log_info "Strona dostępna pod: https://$DOMAIN"
else
    log_error "❌ Problem z konfiguracją SSL"
    log_info "Sprawdź logi: sudo tail -f /var/log/nginx/error.log"
fi

log_info "Konfiguracja SSL zakończona!"
log_info "Certyfikat zostanie automatycznie odnowiony"
