#!/bin/bash

# Skrypt konfiguracji bezpieczeństwa VPS dla aplikacji klimatyzacja-sms-manager
# Autor: Augment Code Assistant

set -e

# Kolory dla outputu
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Sprawdź czy skrypt jest uruchomiony jako root
if [[ $EUID -eq 0 ]]; then
   log_error "Ten skrypt nie powinien być uruchomiony jako root"
   exit 1
fi

log_info "🔒 Konfiguracja bezpieczeństwa VPS"

# Krok 1: Aktualizacja systemu
log_step "1. Aktualizacja systemu..."
sudo apt update && sudo apt upgrade -y

# Krok 2: Instalacja narzędzi bezpieczeństwa
log_step "2. Instalacja narzędzi bezpieczeństwa..."
sudo apt install -y fail2ban ufw unattended-upgrades apt-listchanges

# Krok 3: Konfiguracja UFW (Uncomplicated Firewall)
log_step "3. Konfiguracja firewall..."

# Domyślne zasady
sudo ufw default deny incoming
sudo ufw default allow outgoing

# Zezwól na SSH (zmień port jeśli używasz niestandardowego)
sudo ufw allow ssh
sudo ufw allow 22/tcp

# Zezwól na HTTP i HTTPS
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# Zezwól na port aplikacji (jeśli potrzebne)
sudo ufw allow 3000/tcp

# Włącz firewall
sudo ufw --force enable

log_info "Firewall skonfigurowany:"
sudo ufw status verbose

# Krok 4: Konfiguracja Fail2Ban
log_step "4. Konfiguracja Fail2Ban..."

# Utwórz lokalną konfigurację
sudo tee /etc/fail2ban/jail.local > /dev/null << EOF
[DEFAULT]
# Ban hosts for 1 hour
bantime = 3600

# A host is banned after 3 attempts
maxretry = 3

# "Findtime" is the time between failures
findtime = 600

# Whitelist local IPs
ignoreip = 127.0.0.1/8 ::1

[sshd]
enabled = true
port = ssh
filter = sshd
logpath = /var/log/auth.log
maxretry = 3

[nginx-http-auth]
enabled = true
filter = nginx-http-auth
port = http,https
logpath = /var/log/nginx/error.log

[nginx-limit-req]
enabled = true
filter = nginx-limit-req
port = http,https
logpath = /var/log/nginx/error.log

[nginx-botsearch]
enabled = true
filter = nginx-botsearch
port = http,https
logpath = /var/log/nginx/access.log
maxretry = 2
EOF

# Uruchom i włącz Fail2Ban
sudo systemctl enable fail2ban
sudo systemctl start fail2ban

log_info "Fail2Ban skonfigurowany"

# Krok 5: Automatyczne aktualizacje bezpieczeństwa
log_step "5. Konfiguracja automatycznych aktualizacji..."

sudo tee /etc/apt/apt.conf.d/50unattended-upgrades > /dev/null << EOF
Unattended-Upgrade::Allowed-Origins {
    "\${distro_id}:\${distro_codename}";
    "\${distro_id}:\${distro_codename}-security";
    "\${distro_id}ESMApps:\${distro_codename}-apps-security";
    "\${distro_id}ESM:\${distro_codename}-infra-security";
};

Unattended-Upgrade::Package-Blacklist {
};

Unattended-Upgrade::DevRelease "false";
Unattended-Upgrade::Remove-Unused-Dependencies "true";
Unattended-Upgrade::Automatic-Reboot "false";
Unattended-Upgrade::Automatic-Reboot-Time "02:00";
EOF

sudo tee /etc/apt/apt.conf.d/20auto-upgrades > /dev/null << EOF
APT::Periodic::Update-Package-Lists "1";
APT::Periodic::Unattended-Upgrade "1";
APT::Periodic::AutocleanInterval "7";
EOF

# Krok 6: Konfiguracja SSH (opcjonalnie)
log_step "6. Wzmocnienie konfiguracji SSH..."

# Backup oryginalnej konfiguracji
sudo cp /etc/ssh/sshd_config /etc/ssh/sshd_config.backup

# Podstawowe ustawienia bezpieczeństwa SSH
sudo tee -a /etc/ssh/sshd_config.d/security.conf > /dev/null << EOF
# Disable root login
PermitRootLogin no

# Disable password authentication (uncomment if using SSH keys)
# PasswordAuthentication no

# Disable empty passwords
PermitEmptyPasswords no

# Limit login attempts
MaxAuthTries 3

# Disconnect idle sessions
ClientAliveInterval 300
ClientAliveCountMax 2

# Disable X11 forwarding
X11Forwarding no

# Disable unused authentication methods
ChallengeResponseAuthentication no
KerberosAuthentication no
GSSAPIAuthentication no
EOF

log_warn "SSH konfiguracja zaktualizowana. Sprawdź ustawienia przed restartem SSH!"

# Krok 7: Monitoring i logi
log_step "7. Konfiguracja monitoringu..."

# Logrotate dla aplikacji
sudo tee /etc/logrotate.d/klimatyzacja-sms-manager > /dev/null << EOF
/home/<USER>/.pm2/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 0644 $USER $USER
    postrotate
        pm2 reloadLogs
    endscript
}
EOF

# Krok 8: Tworzenie skryptu monitoringu
log_step "8. Tworzenie skryptu monitoringu..."

tee /home/<USER>/monitor.sh > /dev/null << 'EOF'
#!/bin/bash

# Skrypt monitoringu systemu
LOG_FILE="/home/<USER>/system-monitor.log"
TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')

echo "[$TIMESTAMP] System Monitor Check" >> $LOG_FILE

# Sprawdź użycie dysku
DISK_USAGE=$(df -h / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "[$TIMESTAMP] WARNING: Disk usage is ${DISK_USAGE}%" >> $LOG_FILE
fi

# Sprawdź użycie pamięci
MEM_USAGE=$(free | grep Mem | awk '{printf("%.0f", $3/$2 * 100.0)}')
if [ $MEM_USAGE -gt 80 ]; then
    echo "[$TIMESTAMP] WARNING: Memory usage is ${MEM_USAGE}%" >> $LOG_FILE
fi

# Sprawdź status aplikacji
if ! pm2 status klimatyzacja-sms-manager | grep -q "online"; then
    echo "[$TIMESTAMP] ERROR: Application is not running!" >> $LOG_FILE
fi

# Sprawdź status Nginx
if ! systemctl is-active --quiet nginx; then
    echo "[$TIMESTAMP] ERROR: Nginx is not running!" >> $LOG_FILE
fi
EOF

chmod +x /home/<USER>/monitor.sh

# Dodaj do crontab
(crontab -l 2>/dev/null; echo "*/5 * * * * /home/<USER>/monitor.sh") | crontab -

log_info "🎉 Konfiguracja bezpieczeństwa zakończona!"
log_info ""
log_info "📋 Podsumowanie:"
log_info "✅ Firewall (UFW) skonfigurowany"
log_info "✅ Fail2Ban zainstalowany i skonfigurowany"
log_info "✅ Automatyczne aktualizacje bezpieczeństwa włączone"
log_info "✅ SSH wzmocniony"
log_info "✅ Monitoring skonfigurowany"
log_info ""
log_warn "⚠️  WAŻNE:"
log_warn "1. Sprawdź konfigurację SSH przed restartem: sudo sshd -t"
log_warn "2. Restart SSH: sudo systemctl restart ssh"
log_warn "3. Sprawdź status Fail2Ban: sudo fail2ban-client status"
log_warn "4. Monitoruj logi: tail -f /home/<USER>/system-monitor.log"
