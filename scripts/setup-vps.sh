#!/bin/bash

# Skrypt konfiguracji VPS dla aplikacji klimatyzacja-sms-manager
# Autor: Augment Code Assistant
# Data: $(date)

set -e  # Zatrzymaj skrypt przy błędzie

# Kolory dla outputu
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Funkcje pomocnicze
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Sprawdź czy skrypt jest uruchomiony jako root
if [[ $EUID -eq 0 ]]; then
   log_error "Ten skrypt nie powinien być uruchomiony jako root"
   exit 1
fi

# Zmienne konfiguracyjne
APP_NAME="klimatyzacja-sms-manager"
APP_USER="$USER"
APP_DIR="/home/<USER>/$APP_NAME"
NGINX_AVAILABLE="/etc/nginx/sites-available"
NGINX_ENABLED="/etc/nginx/sites-enabled"
DOMAIN_NAME="${1:-localhost}"  # Pierwszy argument lub localhost jako domyślny

log_info "Rozpoczynam konfigurację VPS dla aplikacji $APP_NAME"
log_info "Domena: $DOMAIN_NAME"
log_info "Użytkownik: $APP_USER"
log_info "Katalog aplikacji: $APP_DIR"

# Aktualizacja systemu
log_info "Aktualizacja systemu..."
sudo apt update && sudo apt upgrade -y

# Instalacja podstawowych narzędzi
log_info "Instalacja podstawowych narzędzi..."
sudo apt install -y curl wget git unzip software-properties-common apt-transport-https ca-certificates gnupg lsb-release

# Instalacja Node.js (najnowsza LTS)
log_info "Instalacja Node.js..."
curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
sudo apt install -y nodejs

# Weryfikacja instalacji Node.js
node_version=$(node --version)
npm_version=$(npm --version)
log_info "Node.js zainstalowany: $node_version"
log_info "npm zainstalowany: $npm_version"

# Instalacja PM2 globalnie
log_info "Instalacja PM2..."
sudo npm install -g pm2

# Instalacja Nginx
log_info "Instalacja Nginx..."
sudo apt install -y nginx

# Uruchomienie i włączenie Nginx
sudo systemctl start nginx
sudo systemctl enable nginx

# Konfiguracja firewall
log_info "Konfiguracja firewall..."
sudo ufw allow OpenSSH
sudo ufw allow 'Nginx Full'
sudo ufw --force enable

# Utworzenie katalogu aplikacji
log_info "Tworzenie katalogu aplikacji..."
mkdir -p "$APP_DIR"
cd "$APP_DIR"

# Klonowanie repozytorium (jeśli nie istnieje)
if [ ! -d ".git" ]; then
    log_info "Klonowanie repozytorium..."
    git clone https://github.com/twojemiastopolecato/klimatyzacja-sms-manager.git .
else
    log_info "Repozytorium już istnieje, aktualizacja..."
    git pull origin main
fi

# Instalacja zależności
log_info "Instalacja zależności aplikacji..."
npm install

# Utworzenie pliku .env z przykładu
if [ ! -f ".env" ]; then
    log_info "Tworzenie pliku .env..."
    cp .env.example .env
    log_warn "UWAGA: Skonfiguruj zmienne środowiskowe w pliku .env"
fi

# Budowanie aplikacji
log_info "Budowanie aplikacji..."
npm run build

# Konfiguracja PM2 startup
log_info "Konfiguracja PM2 startup..."
pm2 startup
log_warn "Uruchom polecenie wyświetlone powyżej jako sudo"

log_info "Konfiguracja VPS zakończona pomyślnie!"
log_info "Następne kroki:"
log_info "1. Skonfiguruj zmienne środowiskowe w $APP_DIR/.env"
log_info "2. Uruchom aplikację: pm2 start ecosystem.config.js"
log_info "3. Skonfiguruj Nginx: sudo cp config/nginx.conf $NGINX_AVAILABLE/$APP_NAME"
log_info "4. Włącz konfigurację Nginx: sudo ln -s $NGINX_AVAILABLE/$APP_NAME $NGINX_ENABLED/"
log_info "5. Przetestuj konfigurację: sudo nginx -t"
log_info "6. Przeładuj Nginx: sudo systemctl reload nginx"
