#!/bin/bash

# Skrypt monitoringu aplikacji klimatyzacja-sms-manager
# Autor: Augment Code Assistant

# Kolory dla outputu
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_status() {
    echo -e "${BLUE}[STATUS]${NC} $1"
}

# Zmienne konfiguracyjne
APP_NAME="klimatyzacja-sms-manager"
LOG_FILE="/home/<USER>/monitoring.log"
TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')
ALERT_EMAIL="${ALERT_EMAIL:-admin@localhost}"

# Funkcja logowania
log_to_file() {
    echo "[$TIMESTAMP] $1" >> "$LOG_FILE"
}

# Funkcja wysyłania alertów (opcjonalnie)
send_alert() {
    local message="$1"
    log_to_file "ALERT: $message"
    
    # Opcjonalnie: wysyłanie emaila (wymaga konfiguracji sendmail/postfix)
    # echo "$message" | mail -s "Alert: $APP_NAME" "$ALERT_EMAIL"
    
    # Opcjonalnie: webhook do Slack/Discord
    # curl -X POST -H 'Content-type: application/json' \
    #   --data "{\"text\":\"$message\"}" \
    #   "$SLACK_WEBHOOK_URL"
}

# Sprawdź status systemu
check_system() {
    log_status "Sprawdzanie systemu..."
    
    # Użycie dysku
    DISK_USAGE=$(df -h / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ "$DISK_USAGE" -gt 85 ]; then
        send_alert "Wysokie użycie dysku: ${DISK_USAGE}%"
    fi
    
    # Użycie pamięci
    MEM_USAGE=$(free | grep Mem | awk '{printf("%.0f", $3/$2 * 100.0)}')
    if [ "$MEM_USAGE" -gt 85 ]; then
        send_alert "Wysokie użycie pamięci: ${MEM_USAGE}%"
    fi
    
    # Load average
    LOAD_AVG=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    CPU_CORES=$(nproc)
    if (( $(echo "$LOAD_AVG > $CPU_CORES" | bc -l) )); then
        send_alert "Wysokie obciążenie CPU: $LOAD_AVG (cores: $CPU_CORES)"
    fi
    
    log_info "System OK - Dysk: ${DISK_USAGE}%, RAM: ${MEM_USAGE}%, Load: $LOAD_AVG"
}

# Sprawdź status aplikacji
check_application() {
    log_status "Sprawdzanie aplikacji..."
    
    # Status PM2
    if ! pm2 status "$APP_NAME" | grep -q "online"; then
        send_alert "Aplikacja $APP_NAME nie działa!"
        log_error "Aplikacja nie działa - próba restartu..."
        pm2 restart "$APP_NAME"
        sleep 5
        
        if ! pm2 status "$APP_NAME" | grep -q "online"; then
            send_alert "Nie udało się zrestartować aplikacji $APP_NAME"
        else
            log_info "Aplikacja zrestartowana pomyślnie"
        fi
    else
        log_info "Aplikacja działa poprawnie"
    fi
    
    # Health check HTTP
    if curl -f -s http://localhost:3000 > /dev/null; then
        log_info "Health check HTTP: OK"
    else
        send_alert "Health check HTTP nie powiódł się"
        log_error "Aplikacja nie odpowiada na HTTP"
    fi
}

# Sprawdź status Nginx
check_nginx() {
    log_status "Sprawdzanie Nginx..."
    
    if systemctl is-active --quiet nginx; then
        log_info "Nginx działa poprawnie"
    else
        send_alert "Nginx nie działa!"
        log_error "Nginx nie działa - próba restartu..."
        sudo systemctl restart nginx
        
        if systemctl is-active --quiet nginx; then
            log_info "Nginx zrestartowany pomyślnie"
        else
            send_alert "Nie udało się zrestartować Nginx"
        fi
    fi
}

# Sprawdź logi błędów
check_logs() {
    log_status "Sprawdzanie logów błędów..."
    
    # Sprawdź logi PM2
    ERROR_COUNT=$(pm2 logs "$APP_NAME" --lines 100 --nostream 2>/dev/null | grep -i error | wc -l)
    if [ "$ERROR_COUNT" -gt 10 ]; then
        send_alert "Dużo błędów w logach aplikacji: $ERROR_COUNT"
    fi
    
    # Sprawdź logi Nginx
    NGINX_ERRORS=$(tail -n 100 /var/log/nginx/error.log 2>/dev/null | grep "$(date '+%Y/%m/%d')" | wc -l)
    if [ "$NGINX_ERRORS" -gt 20 ]; then
        send_alert "Dużo błędów w logach Nginx: $NGINX_ERRORS"
    fi
    
    log_info "Logi sprawdzone - Błędy aplikacji: $ERROR_COUNT, Błędy Nginx: $NGINX_ERRORS"
}

# Sprawdź certyfikat SSL
check_ssl() {
    log_status "Sprawdzanie certyfikatu SSL..."
    
    if [ -f "/etc/letsencrypt/live/*/cert.pem" ]; then
        CERT_FILE=$(find /etc/letsencrypt/live -name "cert.pem" | head -1)
        EXPIRY_DATE=$(openssl x509 -enddate -noout -in "$CERT_FILE" | cut -d= -f2)
        EXPIRY_TIMESTAMP=$(date -d "$EXPIRY_DATE" +%s)
        CURRENT_TIMESTAMP=$(date +%s)
        DAYS_LEFT=$(( (EXPIRY_TIMESTAMP - CURRENT_TIMESTAMP) / 86400 ))
        
        if [ "$DAYS_LEFT" -lt 30 ]; then
            send_alert "Certyfikat SSL wygasa za $DAYS_LEFT dni!"
        fi
        
        log_info "Certyfikat SSL ważny przez $DAYS_LEFT dni"
    else
        log_warn "Brak certyfikatu SSL"
    fi
}

# Sprawdź przestrzeń na backupy
check_backup_space() {
    log_status "Sprawdzanie przestrzeni na backupy..."
    
    BACKUP_DIR="/home/<USER>/backups"
    if [ -d "$BACKUP_DIR" ]; then
        BACKUP_SIZE=$(du -sh "$BACKUP_DIR" | cut -f1)
        BACKUP_COUNT=$(find "$BACKUP_DIR" -name "*.tar.gz" | wc -l)
        
        log_info "Backupy: $BACKUP_COUNT plików, rozmiar: $BACKUP_SIZE"
        
        # Usuń stare backupy jeśli za dużo
        if [ "$BACKUP_COUNT" -gt 50 ]; then
            log_warn "Za dużo plików backup ($BACKUP_COUNT), czyszczenie..."
            find "$BACKUP_DIR" -name "*.tar.gz" -mtime +7 -delete
        fi
    fi
}

# Główna funkcja
main() {
    log_info "🔍 Rozpoczynam monitoring aplikacji $APP_NAME"
    
    check_system
    check_application
    check_nginx
    check_logs
    check_ssl
    check_backup_space
    
    log_info "✅ Monitoring zakończony"
    log_to_file "Monitoring completed successfully"
}

# Sprawdź argumenty
case "${1:-monitor}" in
    "monitor")
        main
        ;;
    "status")
        echo "=== Status Aplikacji ==="
        pm2 status
        echo ""
        echo "=== Status Nginx ==="
        systemctl status nginx --no-pager -l
        echo ""
        echo "=== Użycie zasobów ==="
        echo "Dysk: $(df -h / | awk 'NR==2 {print $5}')"
        echo "RAM: $(free -h | grep Mem | awk '{print $3"/"$2}')"
        echo "Load: $(uptime | awk -F'load average:' '{print $2}')"
        ;;
    "logs")
        echo "=== Logi aplikacji (ostatnie 50 linii) ==="
        pm2 logs "$APP_NAME" --lines 50
        ;;
    "help")
        echo "Użycie: $0 [monitor|status|logs|help]"
        echo "  monitor - pełny monitoring (domyślnie)"
        echo "  status  - pokaż status systemu"
        echo "  logs    - pokaż logi aplikacji"
        echo "  help    - pokaż tę pomoc"
        ;;
    *)
        echo "Nieznana opcja: $1"
        echo "Użyj: $0 help"
        exit 1
        ;;
esac
