#!/bin/bash

# 🚀 Klimatyzacja SMS Manager - Instalator One-Click
# Autor: Augment Code Assistant
# Data: 2025-06-15

set -e

# Kolory dla outputu
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# Funkcje pomocnicze
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

log_success() {
    echo -e "${PURPLE}[SUCCESS]${NC} $1"
}

print_banner() {
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                                                              ║"
    echo "║        🌡️  KLIMATYZACJA SMS MANAGER INSTALLER 🌡️           ║"
    echo "║                                                              ║"
    echo "║              Automatyczny instalator VPS                    ║"
    echo "║                                                              ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

print_footer() {
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                                                              ║"
    echo "║                    🎉 INSTALACJA ZAKOŃCZONA! 🎉             ║"
    echo "║                                                              ║"
    echo "║              Aplikacja jest gotowa do użycia!               ║"
    echo "║                                                              ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# Sprawdź czy skrypt jest uruchomiony jako root
check_user() {
    if [[ $EUID -eq 0 ]]; then
        log_error "Ten skrypt nie powinien być uruchomiony jako root!"
        log_info "Przełącz się na zwykłego użytkownika i uruchom ponownie."
        exit 1
    fi
}

# Zbierz informacje od użytkownika
collect_info() {
    log_step "Zbieranie informacji konfiguracyjnych..."
    
    # Domena
    read -p "🌐 Podaj domenę (lub naciśnij Enter dla localhost): " DOMAIN
    DOMAIN=${DOMAIN:-localhost}
    
    # Email dla SSL
    if [ "$DOMAIN" != "localhost" ]; then
        read -p "📧 Podaj email dla certyfikatu SSL: " EMAIL
        EMAIL=${EMAIL:-admin@$DOMAIN}
    fi
    
    # Supabase URL
    read -p "🗄️  Podaj Supabase URL: " SUPABASE_URL
    while [ -z "$SUPABASE_URL" ]; do
        log_warn "Supabase URL jest wymagany!"
        read -p "🗄️  Podaj Supabase URL: " SUPABASE_URL
    done
    
    # Supabase Anon Key
    read -p "🔑 Podaj Supabase Anon Key: " SUPABASE_KEY
    while [ -z "$SUPABASE_KEY" ]; do
        log_warn "Supabase Anon Key jest wymagany!"
        read -p "🔑 Podaj Supabase Anon Key: " SUPABASE_KEY
    done
    
    echo ""
    log_info "📋 Podsumowanie konfiguracji:"
    log_info "   Domena: $DOMAIN"
    [ "$DOMAIN" != "localhost" ] && log_info "   Email: $EMAIL"
    log_info "   Supabase URL: $SUPABASE_URL"
    log_info "   Supabase Key: ${SUPABASE_KEY:0:20}..."
    echo ""
    
    read -p "❓ Czy kontynuować instalację? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Instalacja anulowana."
        exit 0
    fi
}

# Główna funkcja instalacji
install_app() {
    print_banner
    
    log_step "🚀 Rozpoczynam instalację Klimatyzacja SMS Manager"
    
    # Sprawdź użytkownika
    check_user
    
    # Zbierz informacje
    collect_info
    
    # Krok 1: Konfiguracja VPS
    log_step "1/8 Konfiguracja VPS..."
    if [ -f "scripts/setup-vps.sh" ]; then
        chmod +x scripts/setup-vps.sh
        ./scripts/setup-vps.sh "$DOMAIN"
    else
        log_error "Brak pliku scripts/setup-vps.sh"
        exit 1
    fi
    
    # Krok 2: Konfiguracja zmiennych środowiskowych
    log_step "2/8 Konfiguracja zmiennych środowiskowych..."
    cat > .env << EOF
# Supabase Configuration
VITE_SUPABASE_URL=$SUPABASE_URL
VITE_SUPABASE_ANON_KEY=$SUPABASE_KEY

# Application Configuration
VITE_APP_TITLE=Klimatyzacja SMS Manager
VITE_APP_DESCRIPTION=System zarządzania klimatyzacją przez SMS

# Environment
NODE_ENV=production
VITE_ENVIRONMENT=production
EOF
    
    log_success "Zmienne środowiskowe skonfigurowane"
    
    # Krok 3: Budowanie aplikacji
    log_step "3/8 Budowanie aplikacji..."
    npm install
    npm run build
    log_success "Aplikacja zbudowana"
    
    # Krok 4: Konfiguracja PM2
    log_step "4/8 Konfiguracja PM2..."
    chmod +x scripts/pm2-setup.sh
    ./scripts/pm2-setup.sh
    log_success "PM2 skonfigurowane"
    
    # Krok 5: Konfiguracja Nginx
    log_step "5/8 Konfiguracja Nginx..."
    sudo cp config/nginx.conf /etc/nginx/sites-available/klimatyzacja-sms-manager
    sudo sed -i "s/your-domain.com/$DOMAIN/g" /etc/nginx/sites-available/klimatyzacja-sms-manager
    sudo ln -sf /etc/nginx/sites-available/klimatyzacja-sms-manager /etc/nginx/sites-enabled/
    sudo rm -f /etc/nginx/sites-enabled/default
    
    if sudo nginx -t; then
        sudo systemctl restart nginx
        log_success "Nginx skonfigurowany"
    else
        log_error "Błąd konfiguracji Nginx"
        exit 1
    fi
    
    # Krok 6: SSL (jeśli domena nie jest localhost)
    if [ "$DOMAIN" != "localhost" ]; then
        log_step "6/8 Konfiguracja SSL..."
        chmod +x scripts/setup-ssl.sh
        ./scripts/setup-ssl.sh "$DOMAIN" "$EMAIL"
        log_success "SSL skonfigurowane"
    else
        log_step "6/8 Pomijam SSL (localhost)"
    fi
    
    # Krok 7: Bezpieczeństwo
    log_step "7/8 Konfiguracja bezpieczeństwa..."
    chmod +x scripts/security-setup.sh
    ./scripts/security-setup.sh
    log_success "Bezpieczeństwo skonfigurowane"
    
    # Krok 8: Monitoring
    log_step "8/8 Konfiguracja monitoringu..."
    chmod +x scripts/monitoring.sh
    
    # Dodaj monitoring do crontab
    (crontab -l 2>/dev/null; echo "*/5 * * * * $(pwd)/scripts/monitoring.sh") | crontab -
    log_success "Monitoring skonfigurowany"
    
    # Test końcowy
    log_step "🧪 Test aplikacji..."
    sleep 5
    
    if [ "$DOMAIN" = "localhost" ]; then
        TEST_URL="http://localhost"
    else
        TEST_URL="https://$DOMAIN"
    fi
    
    if curl -f -s "$TEST_URL" > /dev/null; then
        log_success "✅ Aplikacja działa poprawnie!"
    else
        log_warn "⚠️  Aplikacja może nie działać poprawnie. Sprawdź logi."
    fi
    
    print_footer
    
    # Podsumowanie
    echo ""
    log_info "🎯 Aplikacja zainstalowana pomyślnie!"
    echo ""
    log_info "📍 Dostęp do aplikacji:"
    if [ "$DOMAIN" = "localhost" ]; then
        log_info "   🌐 http://$(hostname -I | awk '{print $1}')"
        log_info "   🌐 http://localhost (jeśli korzystasz z tunelu)"
    else
        log_info "   🌐 https://$DOMAIN"
    fi
    echo ""
    log_info "🔧 Przydatne komendy:"
    log_info "   Status:     pm2 status"
    log_info "   Logi:       pm2 logs klimatyzacja-sms-manager"
    log_info "   Restart:    pm2 restart klimatyzacja-sms-manager"
    log_info "   Monitoring: ./scripts/monitoring.sh status"
    log_info "   Backup:     ./scripts/backup.sh"
    echo ""
    log_info "📚 Dokumentacja:"
    log_info "   Przewodnik: docs/deployment-guide.md"
    log_info "   Problemy:   docs/troubleshooting.md"
    echo ""
    log_success "🎉 Gotowe! Miłego korzystania z aplikacji!"
}

# Uruchom instalację
install_app
