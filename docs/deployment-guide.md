# 🚀 Przewodnik Wdrożenia - Klimatyzacja SMS Manager

## 📋 Spis Treści

1. [Wymagania](#wymagania)
2. [Przygotowanie VPS](#przygotowanie-vps)
3. [Konfiguracja Środowiska](#konfiguracja-środowiska)
4. [Wdro<PERSON><PERSON>e Aplikacji](#wdrożenie-aplikacji)
5. [Konfiguracja CI/CD](#konfiguracja-cicd)
6. [Bezpieczeństwo](#bezpieczeństwo)
7. [Monitoring](#monitoring)
8. [Troubleshooting](#troubleshooting)

## 🔧 Wymagania

### Serwer VPS
- **OS**: Ubuntu 20.04+ / Debian 11+
- **RAM**: Minimum 1GB (zalecane 2GB+)
- **Dysk**: Minimum 20GB SSD
- **CPU**: 1 vCore (zalecane 2+)
- **Sieć**: Publiczny adres IP

### Lokalne Narzędzia
- Git
- SSH client
- Node.js 18+ (do lokalnego developmentu)

### Usługi Zewnętrzne
- **Supabase**: Konto i projekt
- **Domena**: Opcjonalnie dla SSL

## 🖥️ Przygotowanie VPS

### 1. Połączenie z VPS
```bash
ssh root@YOUR_VPS_IP
```

### 2. Utworzenie użytkownika
```bash
# Utwórz użytkownika (zamień 'ubuntu' na preferowaną nazwę)
adduser ubuntu
usermod -aG sudo ubuntu

# Konfiguracja SSH dla nowego użytkownika
mkdir -p /home/<USER>/.ssh
cp ~/.ssh/authorized_keys /home/<USER>/.ssh/
chown -R ubuntu:ubuntu /home/<USER>/.ssh
chmod 700 /home/<USER>/.ssh
chmod 600 /home/<USER>/.ssh/authorized_keys
```

### 3. Przełączenie na nowego użytkownika
```bash
su - ubuntu
```

## ⚙️ Konfiguracja Środowiska

### 1. Klonowanie repozytorium
```bash
cd ~
git clone https://github.com/twojemiastopolecato/klimatyzacja-sms-manager.git
cd klimatyzacja-sms-manager
```

### 2. Uruchomienie skryptu konfiguracji
```bash
chmod +x scripts/*.sh
./scripts/setup-vps.sh your-domain.com
```

### 3. Konfiguracja zmiennych środowiskowych
```bash
cp .env.example .env
nano .env
```

Wypełnij następujące zmienne:
```env
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
VITE_APP_TITLE=Klimatyzacja SMS Manager
NODE_ENV=production
```

## 🚀 Wdrożenie Aplikacji

### 1. Budowanie aplikacji
```bash
npm install
npm run build
```

### 2. Konfiguracja PM2
```bash
./scripts/pm2-setup.sh
```

### 3. Konfiguracja Nginx
```bash
# Skopiuj konfigurację Nginx
sudo cp config/nginx.conf /etc/nginx/sites-available/klimatyzacja-sms-manager

# Zaktualizuj domenę w konfiguracji
sudo sed -i 's/your-domain.com/TWOJA_DOMENA/g' /etc/nginx/sites-available/klimatyzacja-sms-manager

# Włącz konfigurację
sudo ln -s /etc/nginx/sites-available/klimatyzacja-sms-manager /etc/nginx/sites-enabled/

# Usuń domyślną konfigurację
sudo rm -f /etc/nginx/sites-enabled/default

# Test konfiguracji
sudo nginx -t

# Restart Nginx
sudo systemctl restart nginx
```

### 4. Konfiguracja SSL (opcjonalnie)
```bash
./scripts/setup-ssl.sh your-domain.com <EMAIL>
```

## 🔄 Konfiguracja CI/CD

### 1. Konfiguracja GitHub Secrets

W repozytorium GitHub, przejdź do **Settings > Secrets and variables > Actions** i dodaj:

```
VPS_HOST=your-server-ip
VPS_USER=ubuntu
VPS_SSH_KEY=your-private-ssh-key
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
```

### 2. Generowanie klucza SSH dla GitHub Actions
```bash
# Na VPS
ssh-keygen -t ed25519 -C "github-actions" -f ~/.ssh/github-actions
cat ~/.ssh/github-actions.pub >> ~/.ssh/authorized_keys

# Skopiuj klucz prywatny do GitHub Secrets
cat ~/.ssh/github-actions
```

### 3. Test automatycznego deploymentu
```bash
# Wykonaj commit i push do main branch
git add .
git commit -m "Setup deployment"
git push origin main
```

## 🔒 Bezpieczeństwo

### 1. Konfiguracja bezpieczeństwa
```bash
./scripts/security-setup.sh
```

### 2. Sprawdzenie statusu bezpieczeństwa
```bash
# Status firewall
sudo ufw status verbose

# Status Fail2Ban
sudo fail2ban-client status

# Test SSH
sudo sshd -t
```

## 📊 Monitoring

### 1. Konfiguracja monitoringu
```bash
chmod +x scripts/monitoring.sh

# Dodaj do crontab (monitoring co 5 minut)
crontab -e
# Dodaj linię:
# */5 * * * * /home/<USER>/klimatyzacja-sms-manager/scripts/monitoring.sh
```

### 2. Przydatne komendy monitoringu
```bash
# Status aplikacji
./scripts/monitoring.sh status

# Logi aplikacji
./scripts/monitoring.sh logs

# Pełny monitoring
./scripts/monitoring.sh monitor
```

## 🔧 Troubleshooting

### Aplikacja nie startuje

1. **Sprawdź logi PM2:**
```bash
pm2 logs klimatyzacja-sms-manager
```

2. **Sprawdź konfigurację:**
```bash
pm2 status
cat .env
```

3. **Restart aplikacji:**
```bash
pm2 restart klimatyzacja-sms-manager
```

### Nginx nie działa

1. **Sprawdź konfigurację:**
```bash
sudo nginx -t
```

2. **Sprawdź logi:**
```bash
sudo tail -f /var/log/nginx/error.log
```

3. **Restart Nginx:**
```bash
sudo systemctl restart nginx
```

### SSL nie działa

1. **Sprawdź certyfikat:**
```bash
sudo certbot certificates
```

2. **Odnów certyfikat:**
```bash
sudo certbot renew --dry-run
```

### Problemy z bazą danych

1. **Sprawdź połączenie z Supabase:**
```bash
curl -I https://your-project.supabase.co
```

2. **Sprawdź zmienne środowiskowe:**
```bash
grep SUPABASE .env
```

### Wysokie użycie zasobów

1. **Sprawdź użycie:**
```bash
htop
df -h
free -h
```

2. **Sprawdź logi:**
```bash
./scripts/monitoring.sh status
```

## 📞 Wsparcie

### Przydatne komendy

```bash
# Status wszystkich usług
systemctl status nginx pm2-ubuntu

# Restart wszystkich usług
sudo systemctl restart nginx
pm2 restart all

# Backup
./scripts/backup.sh

# Monitoring
./scripts/monitoring.sh

# Logi systemowe
journalctl -f
```

### Kontakt

W przypadku problemów:
1. Sprawdź logi aplikacji i systemu
2. Uruchom skrypt monitoringu
3. Sprawdź dokumentację Supabase
4. Skontaktuj się z administratorem systemu

---

**Autor**: Augment Code Assistant
**Data**: 2025-06-15
**Wersja**: 1.0
