import { useAuth } from '@/contexts/AuthContext';
import { UserRole } from '@/types/auth';

export const useRole = () => {
  const { userRole, isAdmin, isMonter } = useAuth();

  const hasRole = (role: UserRole): boolean => {
    return userRole === role;
  };

  const hasAnyRole = (roles: UserRole[]): boolean => {
    return roles.includes(userRole);
  };

  const canAccess = (requiredRole: UserRole): boolean => {
    if (requiredRole === 'admin') {
      return isAdmin;
    }
    if (requiredRole === 'monter') {
      return isAdmin || isMonter; // Admin ma dostęp do wszystkiego
    }
    return false;
  };

  return {
    userRole,
    isAdmin,
    isMonter,
    hasRole,
    hasAnyRole,
    canAccess,
  };
};
