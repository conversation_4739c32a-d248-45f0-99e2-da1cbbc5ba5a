import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import Dashboard from '@/components/Dashboard';
import CustomerManager from '@/components/CustomerManager';
import ScheduleManager from '@/components/ScheduleManager';
import TemplateManager from '@/components/TemplateManager';
import SmsApiConfig from '@/components/SmsApiConfig';
import MonterManager from '@/components/MonterManager';
import UserManagement from '@/components/admin/UserManagement';
import { Loader2 } from 'lucide-react';

const Index = () => {
  const { session, userRole, loading, signOut } = useAuth();
  const [currentPage, setCurrentPage] = useState('dashboard');
  const navigate = useNavigate();

  useEffect(() => {
    if (!loading && !session) {
      navigate('/auth');
    }
  }, [session, loading, navigate]);

  const handleLogout = async () => {
    console.log('User logging out');
    await signOut();
  };

  const handleNavigate = (page: string) => {
    console.log('Navigating to page:', page);
    setCurrentPage(page);
  };

  const handleBack = () => {
    setCurrentPage('dashboard');
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Loader2 className="h-16 w-16 animate-spin text-blue-600" />
      </div>
    );
  }

  if (!session) {
    // Should be redirected by the effect, this is a fallback.
    return null;
  }

  switch (currentPage) {
    case 'customers':
      return <CustomerManager onBack={handleBack} />;
    case 'schedules':
      return <ScheduleManager onBack={handleBack} />;
    case 'templates':
      return <TemplateManager onBack={handleBack} />;
    case 'smsapi':
      return <SmsApiConfig onBack={handleBack} />;
    case 'monters':
      return <MonterManager onBack={handleBack} />;
    case 'users':
      return <UserManagement onBack={handleBack} />;
    case 'dashboard':
    default:
      return (
        <Dashboard 
          userRole={userRole} 
          onNavigate={handleNavigate} 
          onLogout={handleLogout} 
        />
      );
  }
};

export default Index;
