export type UserRole = 'admin' | 'monter';

export interface UserProfile {
  id: string;
  email?: string;
  first_name?: string | null;
  last_name?: string | null;
  phone?: string | null;
  role?: UserRole | null;
  company_name?: string | null;
  is_active?: boolean | null;
  smsapi_token?: string | null;
  subscription_end?: string | null;
  created_at?: string | null;
  updated_at?: string | null;
}

export interface AuthContextType {
  user: UserProfile | null;
  session: any;
  userRole: UserRole;
  isAdmin: boolean;
  isMonter: boolean;
  loading: boolean;
  signOut: () => Promise<void>;
  refreshProfile: () => Promise<void>;
}
