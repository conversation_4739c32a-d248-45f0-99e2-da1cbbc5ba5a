module.exports = {
  apps: [
    {
      // Główna aplikacja - serwer statyczny dla SPA
      name: 'klimatyzacja-sms-manager',
      script: 'serve',
      args: '-s dist -l 3000',
      cwd: '/home/<USER>/klimatyzacja-sms-manager',
      instances: 1,
      exec_mode: 'fork',
      
      // Automatyczne restartowanie
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      
      // Zmienne środowiskowe
      env: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      
      // Logging
      log_file: '/home/<USER>/.pm2/logs/klimatyzacja-sms-manager.log',
      out_file: '/home/<USER>/.pm2/logs/klimatyzacja-sms-manager-out.log',
      error_file: '/home/<USER>/.pm2/logs/klimatyzacja-sms-manager-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      
      // Monitoring
      min_uptime: '10s',
      max_restarts: 10,
      
      // Graceful shutdown
      kill_timeout: 5000,
      
      // Health check
      health_check_grace_period: 3000,
      health_check_fatal_exceptions: true
    },
    
    // Opcjonalnie: Backend API (jeśli masz osobny serwer API)
    {
      name: 'klimatyzacja-api',
      script: 'server.js', // Zmień na właściwy plik serwera
      cwd: '/home/<USER>/klimatyzacja-sms-manager/api',
      instances: 'max', // Wykorzystaj wszystkie CPU cores
      exec_mode: 'cluster',
      
      // Automatyczne restartowanie
      autorestart: true,
      watch: false,
      max_memory_restart: '500M',
      
      // Zmienne środowiskowe
      env: {
        NODE_ENV: 'production',
        PORT: 3001,
        API_PORT: 3001
      },
      
      // Logging
      log_file: '/home/<USER>/.pm2/logs/klimatyzacja-api.log',
      out_file: '/home/<USER>/.pm2/logs/klimatyzacja-api-out.log',
      error_file: '/home/<USER>/.pm2/logs/klimatyzacja-api-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      
      // Monitoring
      min_uptime: '10s',
      max_restarts: 10,
      
      // Graceful shutdown
      kill_timeout: 5000,
      
      // Health check
      health_check_grace_period: 3000,
      health_check_fatal_exceptions: true,
      
      // Load balancing
      instance_var: 'INSTANCE_ID',
      
      // Wyłącz jeśli nie masz backend API
      disabled: true
    }
  ],

  // Deployment configuration
  deploy: {
    production: {
      user: 'ubuntu',
      host: ['your-server-ip'], // Zmień na IP swojego serwera
      ref: 'origin/main',
      repo: 'https://github.com/twojemiastopolecato/klimatyzacja-sms-manager.git',
      path: '/home/<USER>/klimatyzacja-sms-manager',
      
      // Pre-deploy commands
      'pre-deploy-local': '',
      
      // Post-deploy commands
      'post-deploy': 'npm ci && npm run build && pm2 reload ecosystem.config.js --env production',
      
      // Pre-setup commands
      'pre-setup': '',
      
      // Post-setup commands  
      'post-setup': 'ls -la',
      
      // Environment variables
      env: {
        NODE_ENV: 'production'
      }
    },
    
    staging: {
      user: 'ubuntu',
      host: ['staging-server-ip'],
      ref: 'origin/develop',
      repo: 'https://github.com/twojemiastopolecato/klimatyzacja-sms-manager.git',
      path: '/home/<USER>/klimatyzacja-sms-manager-staging',
      'post-deploy': 'npm ci && npm run build && pm2 reload ecosystem.config.js --env staging',
      env: {
        NODE_ENV: 'staging'
      }
    }
  }
};
