# 🚀 Klimatyzacja SMS Manager - Deployment Package

Kompletny pakiet wdrożeniowy dla aplikacji klimatyzacja-sms-manager na VPS z automatyzacją CI/CD.

## 📦 Zawartość Pakietu

```
├── .env.example                    # Przykład zmiennych środowiskowych
├── .github/workflows/
│   ├── deploy.yml                  # Automatyczny deployment
│   └── ci.yml                      # CI/CD pipeline
├── config/
│   └── nginx.conf                  # Konfiguracja Nginx
├── scripts/
│   ├── setup-vps.sh               # Konfiguracja VPS
│   ├── deploy.sh                  # Skrypt deploymentu
│   ├── backup.sh                  # Skrypt backup
│   ├── security-setup.sh          # Konfiguracja bezpieczeństwa
│   ├── setup-ssl.sh               # Konfiguracja SSL
│   ├── pm2-setup.sh               # Konfiguracja PM2
│   └── monitoring.sh              # Monitoring systemu
├── docs/
│   ├── deployment-guide.md        # Kompletny przewodnik
│   └── troubleshooting.md         # Rozwiązywanie problemów
├── ecosystem.config.js            # Konfiguracja PM2
└── README-DEPLOYMENT.md           # Ten plik
```

## 🚀 Szybki Start

### 1. Przygotowanie VPS
```bash
# Połącz się z VPS
ssh root@YOUR_VPS_IP

# Utwórz użytkownika
adduser ubuntu
usermod -aG sudo ubuntu

# Przełącz się na użytkownika
su - ubuntu
```

### 2. Klonowanie i Konfiguracja
```bash
# Klonuj repozytorium
git clone https://github.com/twojemiastopolecato/klimatyzacja-sms-manager.git
cd klimatyzacja-sms-manager

# Uruchom konfigurację VPS
chmod +x scripts/*.sh
./scripts/setup-vps.sh your-domain.com
```

### 3. Konfiguracja Środowiska
```bash
# Skopiuj i edytuj zmienne środowiskowe
cp .env.example .env
nano .env

# Wypełnij:
# VITE_SUPABASE_URL=https://your-project.supabase.co
# VITE_SUPABASE_ANON_KEY=your-anon-key
```

### 4. Wdrożenie
```bash
# Buduj i uruchom aplikację
npm install
npm run build
./scripts/pm2-setup.sh

# Konfiguruj Nginx
sudo cp config/nginx.conf /etc/nginx/sites-available/klimatyzacja-sms-manager
sudo sed -i 's/your-domain.com/TWOJA_DOMENA/g' /etc/nginx/sites-available/klimatyzacja-sms-manager
sudo ln -s /etc/nginx/sites-available/klimatyzacja-sms-manager /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default
sudo nginx -t && sudo systemctl restart nginx
```

### 5. SSL (Opcjonalnie)
```bash
./scripts/setup-ssl.sh your-domain.com <EMAIL>
```

### 6. Bezpieczeństwo
```bash
./scripts/security-setup.sh
```

## 🔄 Automatyzacja CI/CD

### Konfiguracja GitHub Secrets
W repozytorium GitHub, dodaj w **Settings > Secrets**:

```
VPS_HOST=your-server-ip
VPS_USER=ubuntu
VPS_SSH_KEY=your-private-ssh-key
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
```

### Generowanie klucza SSH
```bash
# Na VPS
ssh-keygen -t ed25519 -C "github-actions" -f ~/.ssh/github-actions
cat ~/.ssh/github-actions.pub >> ~/.ssh/authorized_keys

# Skopiuj klucz prywatny do GitHub Secrets
cat ~/.ssh/github-actions
```

## 📊 Monitoring i Utrzymanie

### Przydatne Komendy
```bash
# Status aplikacji
pm2 status
./scripts/monitoring.sh status

# Logi
pm2 logs klimatyzacja-sms-manager
./scripts/monitoring.sh logs

# Backup
./scripts/backup.sh

# Restart
pm2 restart klimatyzacja-sms-manager
sudo systemctl restart nginx
```

### Automatyczny Monitoring
```bash
# Dodaj do crontab
crontab -e

# Dodaj linię (monitoring co 5 minut):
*/5 * * * * /home/<USER>/klimatyzacja-sms-manager/scripts/monitoring.sh
```

## 🔧 Rozwiązywanie Problemów

### Najczęstsze Problemy

1. **Aplikacja nie startuje**
   ```bash
   pm2 logs klimatyzacja-sms-manager
   npm run build
   pm2 restart klimatyzacja-sms-manager
   ```

2. **502 Bad Gateway**
   ```bash
   pm2 status
   sudo nginx -t
   sudo systemctl restart nginx
   ```

3. **SSL nie działa**
   ```bash
   sudo certbot certificates
   sudo certbot renew
   ```

Więcej informacji: `docs/troubleshooting.md`

## 📋 Wymagania Systemowe

### VPS
- **OS**: Ubuntu 20.04+ / Debian 11+
- **RAM**: 1GB+ (zalecane 2GB+)
- **Dysk**: 20GB+ SSD
- **CPU**: 1+ vCore

### Usługi
- **Supabase**: Aktywny projekt
- **Domena**: Opcjonalnie dla SSL

## 🔒 Bezpieczeństwo

Pakiet zawiera:
- ✅ Konfigurację firewall (UFW)
- ✅ Fail2Ban dla ochrony SSH
- ✅ Automatyczne aktualizacje bezpieczeństwa
- ✅ SSL/TLS z Let's Encrypt
- ✅ Bezpieczne nagłówki HTTP
- ✅ Monitoring bezpieczeństwa

## 📞 Wsparcie

1. **Dokumentacja**: `docs/deployment-guide.md`
2. **Troubleshooting**: `docs/troubleshooting.md`
3. **Monitoring**: `./scripts/monitoring.sh help`

## 🎯 Funkcje

### ✅ Gotowe do Użycia
- Kompletna konfiguracja VPS
- Automatyczny deployment z GitHub
- SSL/HTTPS out-of-the-box
- Monitoring i alerty
- Backup i recovery
- Bezpieczeństwo

### 🔄 Automatyzacja
- CI/CD z GitHub Actions
- Automatyczne testy
- Deployment na push do main
- Monitoring 24/7
- Automatyczne backupy

### 🛡️ Bezpieczeństwo
- Firewall skonfigurowany
- Fail2Ban aktywny
- SSL/TLS wymuszony
- Bezpieczne nagłówki
- Monitoring bezpieczeństwa

---

**Utworzono przez**: Augment Code Assistant  
**Data**: 2025-06-15  
**Wersja**: 1.0

🚀 **Gotowe do wdrożenia w 10 minut!**
